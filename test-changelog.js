/**
 * 测试更新日志功能的脚本
 * 用于初始化示例数据和测试API
 */

// 模拟小程序环境
const mockWx = {
  cloud: {
    callFunction: async (options) => {
      console.log(`[Mock] 调用云函数: ${options.name}`)
      console.log(`[Mock] 参数:`, options.data)
      
      // 这里可以模拟不同的响应
      return {
        result: {
          success: true,
          message: '模拟调用成功',
          data: []
        }
      }
    }
  }
}

// 设置全局wx对象
global.wx = mockWx

/**
 * 测试获取更新日志列表
 */
async function testGetChangelogList() {
  console.log('\n=== 测试获取更新日志列表 ===')
  
  try {
    const result = await wx.cloud.callFunction({
      name: 'cloud-functions',
      data: {
        action: 'getChangelogList',
        page: 1,
        pageSize: 10
      }
    })
    
    console.log('API响应:', result.result)
    
    if (result.result.success) {
      console.log(`获取到 ${result.result.data.list?.length || 0} 条更新日志`)
      if (result.result.data.list?.length > 0) {
        console.log('第一条日志:', result.result.data.list[0])
      }
    }
  } catch (error) {
    console.error('测试失败:', error)
  }
}

/**
 * 测试重新初始化数据
 */
async function testReinitData() {
  console.log('\n=== 测试重新初始化数据 ===')
  
  try {
    const result = await wx.cloud.callFunction({
      name: 'cloud-functions',
      data: {
        action: 'reinitChangelogData'
      }
    })
    
    console.log('初始化结果:', result.result)
  } catch (error) {
    console.error('初始化失败:', error)
  }
}

/**
 * 运行所有测试
 */
async function runTests() {
  console.log('开始测试更新日志功能...')
  
  // 先初始化数据
  await testReinitData()
  
  // 等待一下
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // 测试获取数据
  await testGetChangelogList()
  
  console.log('\n测试完成！')
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests().catch(console.error)
}

module.exports = {
  testGetChangelogList,
  testReinitData,
  runTests
}
