/**
 * Dashboard3 - 数据分析主题组件
 * 专注于数据可视化和深度分析
 */

const DashboardBaseService = require('../../core/services/dashboard-base-service')

Component({
  /**
   * 组件的属性列表
   */
  properties: {},

  /**
   * 组件的初始数据
   */
  data: {
    // 状态栏高度
    statusBarHeight: 0,
    
    // 工作履历相关
    hasWorkHistory: false,
    currentWork: null,
    currentWorkId: null,
    currentCompany: '',
    currentPosition: '',
    
    // 时间和日期
    currentTime: '',
    currentDateString: '',
    currentDateText: '',
    
    // 今日数据
    todayWorkMinutes: 0,
    todayIncome: '0.00',
    todayEfficiency: '0%',
    
    // 本周数据
    weekWorkMinutes: 0,
    weekIncome: '0.00',
    weekEfficiency: '0%',
    
    // 趋势数据
    incomeTrendData: [],
    workTimeTrendData: [],
    
    // 对比数据
    weekComparison: {
      workTime: { current: 0, previous: 0, change: 0 },
      income: { current: 0, previous: 0, change: 0 }
    },
    monthComparison: {
      workTime: { current: 0, previous: 0, change: 0 },
      income: { current: 0, previous: 0, change: 0 }
    },
    
    // 数据洞察
    workPattern: '',
    efficiencyTip: '',
    statisticsSummary: '',
    
    // 设置相关
    showSettings: false,
    settingsVisible: false,
    showTrendCharts: true,
    showComparisonData: true,
    chartTimeRange: 'week',
    showDataInsights: true,
    showCurrentWork: true,
    incomeDecimalPlaces: 3,
    
    // 隐私脱敏
    privacyMask: {
      todayIncome: false,
      weekIncome: false
    },
    
    // 货币符号
    currencySymbol: '¥'
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 组件初始化
     */
    onLoad() {
      this.baseService = new DashboardBaseService()
      this.initStatusBar()
      this.initData()
      this.startTimer()
    },

    /**
     * 初始化状态栏高度
     */
    initStatusBar() {
      const systemInfo = wx.getSystemInfoSync()
      this.setData({
        statusBarHeight: systemInfo.statusBarHeight || 44
      })
    },

    /**
     * 初始化数据
     */
    initData() {
      this.loadWorkHistory()
      this.loadDashboardSettings()
      this.updateTimeDisplay()
      this.loadAnalyticsData()
    },

    /**
     * 加载工作履历
     */
    loadWorkHistory() {
      try {
        if (!this.baseService.hasWorkHistory()) {
          this.setData({
            hasWorkHistory: false,
            currentWork: null,
            currentWorkId: null,
            currentCompany: '',
            currentPosition: ''
          })
          return
        }

        const currentWork = this.baseService.getCurrentWork()
        this.setData({
          hasWorkHistory: true,
          currentWork: currentWork,
          currentWorkId: currentWork ? currentWork.id : null,
          currentCompany: currentWork ? currentWork.company : '',
          currentPosition: currentWork ? currentWork.position : ''
        })
      } catch (error) {
        console.error('加载当前工作履历失败:', error)
        this.setData({
          hasWorkHistory: false,
          currentWork: null,
          currentWorkId: null,
          currentCompany: '',
          currentPosition: ''
        })
      }
    },

    /**
     * 加载仪表盘设置
     */
    loadDashboardSettings() {
      const config = this.baseService.getDashboardConfig('dashboard3')
      this.setData({
        showTrendCharts: config.showTrendCharts !== false,
        showComparisonData: config.showComparisonData !== false,
        chartTimeRange: config.chartTimeRange || 'week',
        showDataInsights: config.showDataInsights !== false,
        showCurrentWork: config.showCurrentWork !== false,
        incomeDecimalPlaces: config.incomeDecimalPlaces || 3
      })
    },

    /**
     * 更新时间显示
     */
    updateTimeDisplay() {
      const now = new Date()
      const timeString = now.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
      })
      const dateString = now.toLocaleDateString('zh-CN', {
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      })
      
      this.setData({
        currentTime: timeString,
        currentDateString: dateString,
        currentDateText: this.baseService.formatDate(now)
      })
    },

    /**
     * 加载分析数据
     */
    loadAnalyticsData() {
      if (!this.data.hasWorkHistory) {
        return
      }

      this.loadTodayData()
      this.loadWeekData()
      this.loadTrendData()
      this.loadComparisonData()
      this.generateDataInsights()
    },

    /**
     * 加载今日数据
     */
    loadTodayData() {
      const today = new Date()
      const todayData = this.baseService.getTodayData(today, this.data.currentWorkId)
      
      const efficiency = todayData.workMinutes > 0 ? 
        Math.round((todayData.workMinutes / (todayData.workMinutes + todayData.restMinutes)) * 100) : 0
      
      this.setData({
        todayWorkMinutes: todayData.workMinutes,
        todayIncome: this.baseService.formatCurrency(todayData.dailyIncome, this.data.incomeDecimalPlaces),
        todayEfficiency: `${efficiency}%`
      })
    },

    /**
     * 加载本周数据
     */
    loadWeekData() {
      // 获取本周的工作数据
      const weekData = this.calculateWeekData()
      
      this.setData({
        weekWorkMinutes: weekData.totalMinutes,
        weekIncome: this.baseService.formatCurrency(weekData.totalIncome, this.data.incomeDecimalPlaces),
        weekEfficiency: `${weekData.averageEfficiency}%`
      })
    },

    /**
     * 计算本周数据
     */
    calculateWeekData() {
      const now = new Date()
      const startOfWeek = new Date(now)
      startOfWeek.setDate(now.getDate() - now.getDay())
      startOfWeek.setHours(0, 0, 0, 0)
      
      let totalMinutes = 0
      let totalIncome = 0
      let totalEfficiencySum = 0
      let daysWithData = 0
      
      for (let i = 0; i < 7; i++) {
        const date = new Date(startOfWeek)
        date.setDate(startOfWeek.getDate() + i)
        
        const dayData = this.baseService.getTodayData(date, this.data.currentWorkId)
        if (dayData.workMinutes > 0) {
          totalMinutes += dayData.workMinutes
          totalIncome += dayData.dailyIncome
          
          const efficiency = Math.round((dayData.workMinutes / (dayData.workMinutes + dayData.restMinutes)) * 100)
          totalEfficiencySum += efficiency
          daysWithData++
        }
      }
      
      return {
        totalMinutes,
        totalIncome,
        averageEfficiency: daysWithData > 0 ? Math.round(totalEfficiencySum / daysWithData) : 0
      }
    },

    /**
     * 加载趋势数据
     */
    loadTrendData() {
      const trendData = this.calculateTrendData()
      this.setData({
        incomeTrendData: trendData.income,
        workTimeTrendData: trendData.workTime
      })
    },

    /**
     * 计算趋势数据
     */
    calculateTrendData() {
      const now = new Date()
      const days = this.data.chartTimeRange === 'week' ? 7 : 30
      
      const incomeData = []
      const workTimeData = []
      
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date(now)
        date.setDate(now.getDate() - i)
        
        const dayData = this.baseService.getTodayData(date, this.data.currentWorkId)
        
        incomeData.push({
          date: this.baseService.formatDate(date, 'MM/DD'),
          value: dayData.dailyIncome
        })
        
        workTimeData.push({
          date: this.baseService.formatDate(date, 'MM/DD'),
          value: Math.round(dayData.workMinutes / 60 * 100) / 100 // 转换为小时
        })
      }
      
      return {
        income: incomeData,
        workTime: workTimeData
      }
    },

    /**
     * 加载对比数据
     */
    loadComparisonData() {
      const weekComparison = this.calculateWeekComparison()
      const monthComparison = this.calculateMonthComparison()
      
      this.setData({
        weekComparison,
        monthComparison
      })
    },

    /**
     * 计算周对比数据
     */
    calculateWeekComparison() {
      // 本周数据
      const currentWeekData = this.calculateWeekData()
      
      // 上周数据
      const now = new Date()
      const lastWeekStart = new Date(now)
      lastWeekStart.setDate(now.getDate() - now.getDay() - 7)
      
      let lastWeekMinutes = 0
      let lastWeekIncome = 0
      
      for (let i = 0; i < 7; i++) {
        const date = new Date(lastWeekStart)
        date.setDate(lastWeekStart.getDate() + i)
        
        const dayData = this.baseService.getTodayData(date, this.data.currentWorkId)
        lastWeekMinutes += dayData.workMinutes
        lastWeekIncome += dayData.dailyIncome
      }
      
      const workTimeChange = lastWeekMinutes > 0 ? Math.round(((currentWeekData.totalMinutes - lastWeekMinutes) / lastWeekMinutes) * 100) : 0
      const incomeChange = lastWeekIncome > 0 ? Math.round(((currentWeekData.totalIncome - lastWeekIncome) / lastWeekIncome) * 100) : 0

      return {
        workTime: {
          current: currentWeekData.totalMinutes,
          previous: lastWeekMinutes,
          change: workTimeChange,
          changeAbs: Math.abs(workTimeChange)
        },
        income: {
          current: currentWeekData.totalIncome,
          previous: lastWeekIncome,
          change: incomeChange,
          changeAbs: Math.abs(incomeChange)
        }
      }
    },

    /**
     * 计算月对比数据
     */
    calculateMonthComparison() {
      // 简化实现，返回模拟数据
      const workTimeChange = 5
      const incomeChange = 5

      return {
        workTime: {
          current: this.data.weekWorkMinutes * 4,
          previous: this.data.weekWorkMinutes * 3.8,
          change: workTimeChange,
          changeAbs: Math.abs(workTimeChange)
        },
        income: {
          current: parseFloat(this.data.weekIncome) * 4,
          previous: parseFloat(this.data.weekIncome) * 3.8,
          change: incomeChange,
          changeAbs: Math.abs(incomeChange)
        }
      }
    },

    /**
     * 生成数据洞察
     */
    generateDataInsights() {
      const workPattern = this.analyzeWorkPattern()
      const efficiencyTip = this.generateEfficiencyTip()
      const statisticsSummary = this.generateStatisticsSummary()
      
      this.setData({
        workPattern,
        efficiencyTip,
        statisticsSummary
      })
    },

    /**
     * 分析工作模式
     */
    analyzeWorkPattern() {
      const efficiency = parseInt(this.data.todayEfficiency)
      if (efficiency >= 80) {
        return '🎯 高效工作模式：您今天的工作效率很高，保持这种状态！'
      } else if (efficiency >= 60) {
        return '⚡ 稳定工作模式：工作效率良好，可以适当优化休息安排'
      } else if (efficiency >= 40) {
        return '🔄 平衡工作模式：工作与休息比较均衡，注意提高专注度'
      } else {
        return '🌱 轻松工作模式：今天比较轻松，可以考虑增加工作强度'
      }
    },

    /**
     * 生成效率建议
     */
    generateEfficiencyTip() {
      const weekChange = this.data.weekComparison.workTime.change
      if (weekChange > 10) {
        return '📈 本周工作时长增长明显，注意劳逸结合'
      } else if (weekChange > 0) {
        return '📊 本周工作时长稳步增长，保持良好节奏'
      } else if (weekChange > -10) {
        return '📉 本周工作时长略有下降，可以适当调整'
      } else {
        return '⚠️ 本周工作时长下降较多，建议检查工作安排'
      }
    },

    /**
     * 生成统计摘要
     */
    generateStatisticsSummary() {
      const todayHours = Math.round(this.data.todayWorkMinutes / 60 * 10) / 10
      const weekHours = Math.round(this.data.weekWorkMinutes / 60 * 10) / 10
      return `今日工作 ${todayHours}h，本周累计 ${weekHours}h，收入 ${this.data.currencySymbol}${this.data.weekIncome}`
    },

    /**
     * 启动定时器
     */
    startTimer() {
      this.timer = setInterval(() => {
        this.updateTimeDisplay()
        if (this.data.hasWorkHistory) {
          this.loadTodayData() // 实时更新今日数据
        }
      }, 60000) // 每分钟更新一次
    },

    /**
     * 显示主题切换器
     */
    onShowDashboardSwitcher() {
      this.triggerEvent('showDashboardSwitcher')
    },

    /**
     * 显示设置
     */
    onShowSettings() {
      this.setData({
        showSettings: true
      })
      
      setTimeout(() => {
        this.setData({
          settingsVisible: true
        })
      }, 50)
    },

    /**
     * 隐藏设置
     */
    onHideSettings() {
      this.setData({
        settingsVisible: false
      })
      
      setTimeout(() => {
        this.setData({
          showSettings: false
        })
      }, 300)
    },

    /**
     * 防止模态框内容点击关闭
     */
    onModalContentTap() {
      // 阻止事件冒泡，防止关闭模态框
    },

    /**
     * 切换隐私脱敏
     */
    togglePrivacyMask(e) {
      const field = e.currentTarget.dataset.field
      const currentMask = this.data.privacyMask[field]
      
      this.setData({
        [`privacyMask.${field}`]: !currentMask
      })
    },

    /**
     * 跳转到工作履历页面
     */
    goToWorkHistory() {
      wx.navigateTo({
        url: '/pages/work-history/index'
      })
    },

    /**
     * 格式化时间
     */
    formatMinutes(minutes) {
      const hours = Math.floor(minutes / 60)
      const mins = minutes % 60
      return hours > 0 ? `${hours}h${mins}m` : `${mins}m`
    },

    /**
     * 设置项改变处理
     */
    onSettingChange(e) {
      const key = e.currentTarget.dataset.key
      const value = e.detail.value

      this.setData({
        [key]: value
      })

      // 保存设置
      this.baseService.updateDashboardConfig('dashboard3', {
        [key]: value
      })

      // 如果是影响数据显示的设置，重新加载数据
      if (key === 'chartTimeRange') {
        this.loadTrendData()
      }
    },

    /**
     * 时间范围改变处理
     */
    onTimeRangeChange(e) {
      const index = e.detail.value
      const timeRange = index === 0 ? 'week' : 'month'

      this.setData({
        chartTimeRange: timeRange
      })

      // 保存设置
      this.baseService.updateDashboardConfig('dashboard3', {
        chartTimeRange: timeRange
      })

      // 重新加载趋势数据
      this.loadTrendData()
    },

    /**
     * 小数位数改变处理
     */
    onDecimalPlacesChange(e) {
      const decimalPlaces = parseInt(e.detail.value)

      this.setData({
        incomeDecimalPlaces: decimalPlaces
      })

      // 保存设置
      this.baseService.updateDashboardConfig('dashboard3', {
        incomeDecimalPlaces: decimalPlaces
      })

      // 重新格式化收入显示
      this.setData({
        todayIncome: this.baseService.formatCurrency(parseFloat(this.data.todayIncome), decimalPlaces),
        weekIncome: this.baseService.formatCurrency(parseFloat(this.data.weekIncome), decimalPlaces)
      })
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.onLoad()
    },
    
    detached() {
      if (this.timer) {
        clearInterval(this.timer)
      }
    }
  }
})
