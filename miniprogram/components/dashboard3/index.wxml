<!-- Dashboard3 - 数据分析主题组件 -->
<view class="dashboard3-container">

  <!-- Dashboard3 导航栏 -->
  <view class="dashboard3-navbar">
    <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
    <view class="navbar-content">
      <!-- 左侧按钮组 -->
      <view class="navbar-left">
        <view class="navbar-button" bindtap="onShowDashboardSwitcher">
          <image src="/images/icons/palette.svg"></image>
        </view>
        <view class="navbar-button" bindtap="onShowSettings">
          <image src="/images/icons/settings.svg"></image>
        </view>
      </view>

      <!-- 中间标题 -->
      <view class="navbar-center">
        <text class="navbar-title">时间跟踪器</text>
        <text class="navbar-subtitle">数据分析</text>
      </view>

      <!-- 右侧留空，避免被微信菜单覆盖 -->
      <view class="navbar-right"></view>
    </view>
  </view>

  <!-- 无工作履历引导 -->
  <view wx:if="{{!hasWorkHistory}}" class="no-work-guide">
    <view class="guide-content">
      <view class="guide-icon">📈</view>
      <view class="guide-title">欢迎使用数据分析主题</view>
      <view class="guide-text">
        <text>请先添加您的工作履历以查看详细的数据分析</text>
      </view>
      <button class="guide-btn" bindtap="goToWorkHistory">
        <text class="btn-icon">✨</text>
        <text>添加工作履历</text>
      </button>
    </view>
  </view>

  <!-- 主内容区域 -->
  <view wx:else class="main-content">
    
    <!-- 当前工作信息 -->
    <view wx:if="{{showCurrentWork && currentWork}}" class="current-work-section">
      <view class="work-info">
        <text class="company-name">{{currentCompany}}</text>
        <text class="position-name">{{currentPosition}}</text>
      </view>
      <view class="time-display">
        <text class="current-date">{{currentDateString}}</text>
        <text class="current-time">{{currentTime}}</text>
      </view>
    </view>

    <!-- 概览卡片区 -->
    <view class="overview-section">
      <view class="overview-title">📊 数据概览</view>
      
      <!-- 今日数据卡片 -->
      <view class="data-card today-card">
        <view class="card-header">
          <text class="card-title">今日数据</text>
          <text class="card-icon">📅</text>
        </view>
        <view class="card-content">
          <view class="data-item">
            <text class="data-label">工作时长</text>
            <text class="data-value">{{formatMinutes(todayWorkMinutes)}}</text>
          </view>
          <view class="data-item" bindtap="togglePrivacyMask" data-field="todayIncome">
            <text class="data-label">收入</text>
            <text class="data-value">{{currencySymbol}}{{privacyMask.todayIncome ? '***.**' : todayIncome}}</text>
          </view>
          <view class="data-item">
            <text class="data-label">效率</text>
            <text class="data-value">{{todayEfficiency}}</text>
          </view>
        </view>
      </view>

      <!-- 本周数据卡片 -->
      <view class="data-card week-card">
        <view class="card-header">
          <text class="card-title">本周数据</text>
          <text class="card-icon">📈</text>
        </view>
        <view class="card-content">
          <view class="data-item">
            <text class="data-label">总工作时长</text>
            <text class="data-value">{{formatMinutes(weekWorkMinutes)}}</text>
          </view>
          <view class="data-item" bindtap="togglePrivacyMask" data-field="weekIncome">
            <text class="data-label">总收入</text>
            <text class="data-value">{{currencySymbol}}{{privacyMask.weekIncome ? '***.**' : weekIncome}}</text>
          </view>
          <view class="data-item">
            <text class="data-label">平均效率</text>
            <text class="data-value">{{weekEfficiency}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 趋势分析区 -->
    <view wx:if="{{showTrendCharts}}" class="trend-section">
      <view class="section-title">📊 趋势分析</view>
      
      <view class="trend-charts">
        <view class="trend-card">
          <view class="trend-header">
            <text class="trend-title">收入趋势</text>
            <text class="trend-period">最近{{chartTimeRange === 'week' ? '7天' : '30天'}}</text>
          </view>
          <view class="trend-chart">
            <!-- 简化的趋势图表示 -->
            <view class="chart-placeholder">
              <text class="chart-text">📈 收入趋势图</text>
              <text class="chart-summary">{{incomeTrendData.length > 0 ? '数据加载完成' : '暂无数据'}}</text>
            </view>
          </view>
        </view>

        <view class="trend-card">
          <view class="trend-header">
            <text class="trend-title">工作时长趋势</text>
            <text class="trend-period">最近{{chartTimeRange === 'week' ? '7天' : '30天'}}</text>
          </view>
          <view class="trend-chart">
            <!-- 简化的趋势图表示 -->
            <view class="chart-placeholder">
              <text class="chart-text">⏰ 工作时长趋势图</text>
              <text class="chart-summary">{{workTimeTrendData.length > 0 ? '数据加载完成' : '暂无数据'}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 对比分析区 -->
    <view wx:if="{{showComparisonData}}" class="comparison-section">
      <view class="section-title">📊 对比分析</view>
      
      <view class="comparison-cards">
        <!-- 周对比 -->
        <view class="comparison-card">
          <view class="comparison-header">
            <text class="comparison-title">本周 vs 上周</text>
          </view>
          <view class="comparison-content">
            <view class="comparison-item">
              <text class="comparison-label">工作时长</text>
              <view class="comparison-value">
                <text class="change-indicator {{weekComparison.workTime.change >= 0 ? 'positive' : 'negative'}}">
                  {{weekComparison.workTime.change >= 0 ? '↗' : '↘'}} {{weekComparison.workTime.changeAbs}}%
                </text>
              </view>
            </view>
            <view class="comparison-item">
              <text class="comparison-label">收入</text>
              <view class="comparison-value">
                <text class="change-indicator {{weekComparison.income.change >= 0 ? 'positive' : 'negative'}}">
                  {{weekComparison.income.change >= 0 ? '↗' : '↘'}} {{weekComparison.income.changeAbs}}%
                </text>
              </view>
            </view>
          </view>
        </view>

        <!-- 月对比 -->
        <view class="comparison-card">
          <view class="comparison-header">
            <text class="comparison-title">本月 vs 上月</text>
          </view>
          <view class="comparison-content">
            <view class="comparison-item">
              <text class="comparison-label">工作时长</text>
              <view class="comparison-value">
                <text class="change-indicator {{monthComparison.workTime.change >= 0 ? 'positive' : 'negative'}}">
                  {{monthComparison.workTime.change >= 0 ? '↗' : '↘'}} {{monthComparison.workTime.changeAbs}}%
                </text>
              </view>
            </view>
            <view class="comparison-item">
              <text class="comparison-label">收入</text>
              <view class="comparison-value">
                <text class="change-indicator {{monthComparison.income.change >= 0 ? 'positive' : 'negative'}}">
                  {{monthComparison.income.change >= 0 ? '↗' : '↘'}} {{monthComparison.income.changeAbs}}%
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 数据洞察区 -->
    <view wx:if="{{showDataInsights}}" class="insights-section">
      <view class="section-title">🔍 数据洞察</view>
      
      <view class="insights-cards">
        <view class="insight-card">
          <view class="insight-content">
            <text class="insight-text">{{workPattern}}</text>
          </view>
        </view>
        
        <view class="insight-card">
          <view class="insight-content">
            <text class="insight-text">{{efficiencyTip}}</text>
          </view>
        </view>
        
        <view class="insight-card">
          <view class="insight-content">
            <text class="insight-text">{{statisticsSummary}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- Dashboard3 设置模态框 -->
  <view wx:if="{{showSettings}}" class="settings-overlay {{settingsVisible ? 'show' : ''}}" bindtap="onHideSettings">
    <view class="settings-content" catchtap="onModalContentTap">
      <view class="settings-header">
        <text class="settings-title">数据分析主题设置</text>
        <view class="settings-close" bindtap="onHideSettings">✕</view>
      </view>
      
      <view class="settings-body">
        <view class="setting-group">
          <view class="setting-item">
            <text class="setting-label">显示趋势图表</text>
            <switch checked="{{showTrendCharts}}" bindchange="onSettingChange" data-key="showTrendCharts"/>
          </view>
          
          <view class="setting-item">
            <text class="setting-label">显示对比数据</text>
            <switch checked="{{showComparisonData}}" bindchange="onSettingChange" data-key="showComparisonData"/>
          </view>
          
          <view class="setting-item">
            <text class="setting-label">显示数据洞察</text>
            <switch checked="{{showDataInsights}}" bindchange="onSettingChange" data-key="showDataInsights"/>
          </view>
          
          <view class="setting-item">
            <text class="setting-label">显示当前工作</text>
            <switch checked="{{showCurrentWork}}" bindchange="onSettingChange" data-key="showCurrentWork"/>
          </view>
        </view>
        
        <view class="setting-group">
          <view class="setting-item">
            <text class="setting-label">图表时间范围</text>
            <picker mode="selector" range="{{['周', '月']}}" range-key="name" value="{{chartTimeRange === 'week' ? 0 : 1}}" bindchange="onTimeRangeChange">
              <view class="picker-display">{{chartTimeRange === 'week' ? '周' : '月'}}</view>
            </picker>
          </view>
          
          <view class="setting-item">
            <text class="setting-label">收入小数位数</text>
            <picker mode="selector" range="{{[0, 1, 2, 3]}}" value="{{incomeDecimalPlaces}}" bindchange="onDecimalPlacesChange">
              <view class="picker-display">{{incomeDecimalPlaces}}位</view>
            </picker>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
