/* Dashboard3 - 数据分析主题样式 */

.dashboard3-container {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f9f0 0%, #e8f5e8 100%);
  position: relative;
}

/* 导航栏样式 */
.dashboard3-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #2E8B57 0%, #3CB371 50%, #90EE90 100%);
  box-shadow: 0 2rpx 20rpx rgba(46, 139, 87, 0.3);
}

.status-bar {
  width: 100%;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  position: relative;
}

.navbar-left {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.navbar-button {
  width: 64rpx;
  height: 64rpx;
  border-radius: 32rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.navbar-button:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.navbar-button image {
  width: 36rpx;
  height: 36rpx;
  filter: brightness(0) invert(1);
}

.navbar-center {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  line-height: 1;
}

.navbar-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 4rpx;
}

.navbar-right {
  width: 152rpx;
}

/* 主内容区域 */
.main-content {
  margin-top: 132rpx;
  padding: 32rpx;
  min-height: calc(100vh - 132rpx);
}

/* 当前工作信息 */
.current-work-section {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(46, 139, 87, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.work-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.company-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #2E8B57;
}

.position-name {
  font-size: 28rpx;
  color: #666;
}

.time-display {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4rpx;
}

.current-date {
  font-size: 24rpx;
  color: #999;
}

.current-time {
  font-size: 36rpx;
  font-weight: 600;
  color: #2E8B57;
}

/* 概览区域 */
.overview-section {
  margin-bottom: 32rpx;
}

.overview-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2E8B57;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.data-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(46, 139, 87, 0.1);
  border-left: 8rpx solid #3CB371;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.card-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.card-icon {
  font-size: 32rpx;
}

.card-content {
  display: flex;
  justify-content: space-between;
  gap: 24rpx;
}

.data-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.data-label {
  font-size: 24rpx;
  color: #999;
}

.data-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #2E8B57;
}

/* 趋势分析区域 */
.trend-section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2E8B57;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.trend-charts {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.trend-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(46, 139, 87, 0.1);
}

.trend-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.trend-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.trend-period {
  font-size: 24rpx;
  color: #999;
}

.chart-placeholder {
  height: 200rpx;
  background: linear-gradient(135deg, #f0f9f0 0%, #e8f5e8 100%);
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.chart-text {
  font-size: 28rpx;
  color: #2E8B57;
  font-weight: 600;
}

.chart-summary {
  font-size: 24rpx;
  color: #999;
}

/* 对比分析区域 */
.comparison-section {
  margin-bottom: 32rpx;
}

.comparison-cards {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.comparison-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(46, 139, 87, 0.1);
}

.comparison-header {
  margin-bottom: 24rpx;
}

.comparison-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.comparison-content {
  display: flex;
  justify-content: space-between;
  gap: 24rpx;
}

.comparison-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.comparison-label {
  font-size: 24rpx;
  color: #999;
}

.change-indicator {
  font-size: 28rpx;
  font-weight: 600;
}

.change-indicator.positive {
  color: #52c41a;
}

.change-indicator.negative {
  color: #ff4d4f;
}

/* 数据洞察区域 */
.insights-section {
  margin-bottom: 32rpx;
}

.insights-cards {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.insight-card {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(46, 139, 87, 0.1);
  border-left: 4rpx solid #90EE90;
}

.insight-content {
  display: flex;
  align-items: center;
}

.insight-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}

/* 无工作履历引导 */
.no-work-guide {
  margin-top: 132rpx;
  padding: 64rpx 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 200rpx);
}

.guide-content {
  text-align: center;
  max-width: 600rpx;
}

.guide-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.guide-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2E8B57;
  margin-bottom: 24rpx;
}

.guide-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 48rpx;
}

.guide-btn {
  background: linear-gradient(135deg, #2E8B57 0%, #3CB371 100%);
  color: white;
  border: none;
  border-radius: 48rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  box-shadow: 0 8rpx 24rpx rgba(46, 139, 87, 0.3);
  transition: all 0.3s ease;
}

.guide-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(46, 139, 87, 0.3);
}

.btn-icon {
  font-size: 24rpx;
}

/* 设置模态框 */
.settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.settings-overlay.show {
  opacity: 1;
}

.settings-content {
  background: white;
  border-radius: 24rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.settings-overlay.show .settings-content {
  transform: scale(1);
}

.settings-header {
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #2E8B57 0%, #3CB371 100%);
  color: white;
}

.settings-title {
  font-size: 32rpx;
  font-weight: 600;
}

.settings-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
}

.settings-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.setting-group {
  margin-bottom: 32rpx;
}

.setting-group:last-child {
  margin-bottom: 0;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f5f5f5;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  font-size: 28rpx;
  color: #333;
}

.picker-display {
  font-size: 28rpx;
  color: #2E8B57;
  padding: 12rpx 24rpx;
  background: #f0f9f0;
  border-radius: 12rpx;
}
